import React, { useState, useEffect } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import 'react-pdf/dist/Page/AnnotationLayer.css';
import 'react-pdf/dist/Page/TextLayer.css';

// Set up PDF.js worker with multiple fallbacks
if (typeof window !== 'undefined') {
  pdfjs.GlobalWorkerOptions.workerSrc = new URL(
    'pdfjs-dist/build/pdf.worker.min.js',
    import.meta.url,
  ).toString();
}

interface CustomPDFViewerProps {
  pdfUrl: string;
}

const CustomPDFViewer: React.FC<CustomPDFViewerProps> = ({ pdfUrl }) => {
  const [numPages, setNumPages] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    console.log('PDF URL:', pdfUrl);
  }, [pdfUrl]);

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    console.log('PDF loaded successfully with', numPages, 'pages');
    setNumPages(numPages);
    setLoading(false);
    setError(null);
  };

  const onDocumentLoadError = (error: Error) => {
    console.error('PDF loading error:', error);
    setError(`Failed to load PDF: ${error.message}`);
    setLoading(false);
  };

  const onDocumentLoadProgress = ({ loaded, total }: { loaded: number; total: number }) => {
    console.log('PDF loading progress:', Math.round((loaded / total) * 100), '%');
  };

  if (loading) {
    return (
      <div className="pdf-loading">
        <div className="loading-spinner"></div>
        <h3>Loading PDF...</h3>
        <p>Please wait while we prepare the document</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="pdf-error">
        <h3>Error Loading PDF</h3>
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div className="custom-pdf-viewer">
      <Document
        file={pdfUrl}
        onLoadSuccess={onDocumentLoadSuccess}
        onLoadError={onDocumentLoadError}
        onLoadProgress={onDocumentLoadProgress}
        options={{
          cMapUrl: 'https://unpkg.com/pdfjs-dist@3.11.174/cmaps/',
          cMapPacked: true,
          standardFontDataUrl: 'https://unpkg.com/pdfjs-dist@3.11.174/standard_fonts/',
        }}
        loading={
          <div className="pdf-loading">
            <div className="loading-spinner"></div>
            <h3>Loading PDF...</h3>
            <p>Please wait while we prepare the document</p>
          </div>
        }
        error={
          <div className="pdf-error">
            <h3>Error Loading PDF</h3>
            <p>{error || 'Unable to load the PDF document'}</p>
            <button
              onClick={() => window.location.reload()}
              style={{
                marginTop: '16px',
                padding: '8px 16px',
                background: 'rgba(255,255,255,0.1)',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '4px',
                color: 'white',
                cursor: 'pointer'
              }}
            >
              Retry
            </button>
          </div>
        }
      >
        <div className="pdf-pages-scroll">
          {Array.from(new Array(numPages), (el, index) => (
            <div key={`page_${index + 1}`} className="pdf-page-container">
              <Page
                pageNumber={index + 1}
                width={undefined}
                height={undefined}
                className="pdf-page"
                renderTextLayer={false}
                renderAnnotationLayer={false}
                loading={
                  <div className="page-loading">
                    <div className="loading-spinner-small"></div>
                    <p>Loading page {index + 1}...</p>
                  </div>
                }
              />
            </div>
          ))}
        </div>
      </Document>
    </div>
  );
};

export default CustomPDFViewer;
