
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import GradientButton from './GradientButton';
import { useIsMobile } from '../hooks/use-mobile';
import VimeoModal from './VimeoModal';

const DemoReelSection: React.FC = () => {
  const isMobile = useIsMobile();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalContent, setModalContent] = useState<{
    type: 'video' | 'pdf';
    videoId?: string;
    pdfUrl?: string;
  }>({ type: 'video' });

  const handlePlayClick = () => {
    setModalContent({
      type: 'video',
      videoId: '1103544287'
    });
    setIsModalOpen(true);
  };

  const handleLearnMoreClick = () => {
    setModalContent({
      type: 'pdf',
      pdfUrl: '/assets/fory_studio.pdf'
    });
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setModalContent({ type: 'video' });
  };

  return (
    <section className="pt-0 lg:pt-32 pb-20 lg:pb-0 px-4">
      <div className="max-w-7xl mx-auto">
        {/* Section title */}
        <motion.h2
          initial={{ opacity: 0, y: 100 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.6, ease: "easeInOut" }}
          className="text-6xl lg:text-[12rem] xl:text-[14rem] 2xl:text-[16rem] font-normal mb-16 lg:mb-[-0.2em] tracking-tight text-center relative"
          style={{
            background: 'linear-gradient(180deg, #000000 0%, #235F49 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            color: 'transparent',
          }}
        >
          Demo Reel
        </motion.h2>

        <div className="flex flex-col lg:flex-row justify-center items-center lg:items-start gap-0 relative">
          {/* Main video area */}
          <div className="flex items-center justify-center w-full lg:w-auto">
            <div className="relative max-w-[540px] lg:max-w-[1100px] xl:max-w-[1300px] mx-auto">
              <picture>
                <source srcSet="/assets/Ipad-mobile.png" media="(max-width: 1023px)" />
                <img
                  src="/assets/Ipad.png"
                  alt="Demo reel preview"
                  className="w-full h-auto rounded-2xl min-h-[350px] lg:min-h-0"
                />
              </picture>
              <div className="absolute inset-0 rounded-2xl"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <motion.div
                  className="w-20 h-20 bg-white/90 rounded-full flex items-center justify-center cursor-pointer shadow-lg"
                  whileHover={{
                    scale: 1.1,
                    backgroundColor: "rgba(255, 255, 255, 0.95)",
                    boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
                  }}
                  whileTap={{ scale: 0.95 }}
                  transition={{ duration: 0.2, ease: "easeInOut" }}
                  onClick={handlePlayClick}
                >
                  <motion.div
                    className="w-0 h-0 border-l-[20px] border-l-forest-green border-t-[12px] border-t-transparent border-b-[12px] border-b-transparent ml-1"
                    whileHover={{
                      scale: 1.1,
                      borderLeftColor: "#1a5f3a"
                    }}
                    transition={{ duration: 0.2, ease: "easeInOut" }}
                  ></motion.div>
                </motion.div>
              </div>
            </div>
          </div>

          {/* iPhone positioned to center of "Reel" word */}
          <div className="mt-8 lg:mt-0 lg:relative lg:ml-8 flex flex-col items-center lg:items-start">
            <div className="relative max-w-[600px] lg:max-w-[450px] xl:max-w-[550px] mx-auto lg:mx-0">
              {/* Replace the image with a video when ready. Place iphone.webm in /assets/iphone.webm */}
              <video
                src="/assets/iphone.webm"
                className="w-full rounded-2xl"
                autoPlay
                loop
                muted
                playsInline
                poster="/assets/Iphone.png" // fallback poster until video is ready
              >
                Your browser does not support the video tag.
              </video>
              {/* <img 
                src="/assets/Iphone.png"
                alt="Mobile creative work"
                className="w-full rounded-2xl"
              /> */}
            </div>
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.3 }}
              transition={{ duration: 0.6, ease: "easeInOut", delay: 0.1 }}
              className="text-center w-full mt-8"
            >
              <motion.div
                whileHover={!isMobile ? {
                  scale: 1.05,
                  y: -2,
                  transition: { duration: 0.2 }
                } : undefined}
                whileTap={{
                  scale: 0.95,
                  transition: { duration: 0.1 }
                }}
                onClick={handleLearnMoreClick}
                style={{ cursor: 'pointer' }}
              >
                <GradientButton className="text-base px-6 py-3 sm:text-lg sm:px-8 sm:py-3 md:text-xl md:px-10 md:py-4">
                  see more
                </GradientButton>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Vimeo Modal */}
      <VimeoModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        contentType={modalContent.type}
        videoId={modalContent.videoId}
        pdfUrl={modalContent.pdfUrl}
      />
    </section>
  );
};

export default DemoReelSection;
