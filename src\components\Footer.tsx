import React from 'react';
import { useIsMobile } from '@/hooks/use-mobile';

const Footer = () => {
  const isMobile = useIsMobile();

  return (
    <footer className="w-full bg-gradient-to-br from-green-100 via-emerald-50 to-teal-100 px-2 sm:px-4 py-6 sm:py-10 flex flex-col items-center justify-center min-h-[120px] sm:min-h-[160px] relative">
      <div className="w-full max-w-7xl flex flex-col items-center justify-center">
        <div
          className={
            isMobile
              ? 'w-full flex flex-col items-center justify-center mb-4 space-y-4'
              : 'w-full flex flex-row items-center justify-between mb-4'
          }
        >
          {/* Logo left */}
          <div
            className={
              isMobile
                ? 'flex items-center justify-center min-w-[80px]'
                : 'flex items-center justify-start pl-4 min-w-[120px]'
            }
          >
            <img
              src="/assets/Fory logo.png"
              alt="Fory Logo"
              className={isMobile ? 'h-10 object-contain' : 'h-10 sm:h-14 object-contain'}
            />
          </div>
          {/* Center: icons above nav */}
          <div className={isMobile ? 'flex flex-col items-center justify-center flex-1 w-full' : 'flex flex-col items-center justify-center flex-1'}>
            <div className={isMobile ? 'flex flex-row items-center justify-center space-x-6 mb-6' : 'flex flex-row items-center justify-center space-x-8 mb-8'}>
              <a href="mailto:<EMAIL>" target="_blank" rel="noopener noreferrer">
                <img src="/assets/mail icon.png" alt="Mail" className={isMobile ? 'h-6 w-6 object-contain' : 'h-6 w-6 sm:h-7 sm:w-7 object-contain'} />
              </a>
              <a href="https://instagram.com/forystudio" target="_blank" rel="noopener noreferrer">
                <img src="/assets/instagram icon.png" alt="Instagram" className={isMobile ? 'h-6 w-6 object-contain' : 'h-6 w-6 sm:h-7 sm:w-7 object-contain'} />
              </a>
            </div>
            <nav className={isMobile ? 'flex flex-col items-center justify-center space-y-2 mt-2' : 'flex flex-row items-center justify-center space-x-8'}>
              <a href="#home" className="text-base font-semibold text-gray-700 hover:text-black transition-colors">home</a>
              <a href="#gallery" className="text-base font-semibold text-gray-700 hover:text-black transition-colors">gallery</a>
              <a href="#services" className="text-base font-semibold text-gray-700 hover:text-black transition-colors">services</a>
              <a href="#reputation" className="text-base font-semibold text-gray-700 hover:text-black transition-colors">reputation</a>
            </nav>
          </div>
          {/* Spacer right for symmetry */}
          {!isMobile && <div className="min-w-[120px]" />}
        </div>
        {/* Divider line */}
        <hr className="w-[90%] border-t border-black/60 mb-2" />
        {/* Copyright */}
        <div className={isMobile ? 'w-full flex flex-row items-center justify-center' : 'w-full flex flex-row items-center justify-start pl-4'}>
          <span className="text-xs text-gray-700">2023 Fory Studio All Rights Reserved</span>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
