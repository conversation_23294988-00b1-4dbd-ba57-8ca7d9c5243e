
import React from 'react';

interface GradientButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
}

const GradientButton: React.FC<GradientButtonProps> = ({ children, onClick, className = "" }) => {
  return (
    <button
      onClick={onClick}
      className={`bg-gradient-to-r from-emerald-dark via-black/80 to-emerald-dark 
                 text-white font-semibold py-4 px-8 rounded-full 
                 hover:scale-105 transform transition-all duration-300 
                 shadow-lg hover:shadow-xl ${className}`}
    >
      {children}
    </button>
  );
};

export default GradientButton;
