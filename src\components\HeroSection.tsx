
import React, { useRef, useState } from 'react';
import GradientButton from './GradientButton';
import { motion, useAnimation, useInView } from 'framer-motion';
import { useEffect } from 'react';
import { Skeleton } from './ui/skeleton';

const HeroSection: React.FC = () => {
  const controls = useAnimation();
  const titleControls = useAnimation();
  const [videoLoaded, setVideoLoaded] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const taglineRef = useRef<HTMLDivElement>(null);
  const isTaglineInView = useInView(taglineRef, { once: true, amount: 0.3 });

  // Hide body overflow while loading
  useEffect(() => {
    if (!videoLoaded) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    return () => {
      document.body.style.overflow = '';
    };
  }, [videoLoaded]);

  // Trigger title animation when video starts playing
  useEffect(() => {
    if (videoLoaded) {
      // Start title animation with a slight delay after video loads
      setTimeout(() => {
        titleControls.start({ opacity: 1, y: 0 });
      }, 300);
    }
  }, [videoLoaded, titleControls]);

  return (
    <section id="home" className="hero relative min-h-screen overflow-hidden pt-20 pb-0 lg:pb-32">
      {/* Preloader overlay */}
      {!videoLoaded && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-white/90">
          <Skeleton className="w-32 h-32 rounded-full" />
        </div>
      )}
      {/* Main content, hidden until video loads */}
      <div className={`relative z-10 pt-16 pb-8 md:pb-16 transition-opacity duration-500 ${videoLoaded ? 'opacity-100' : 'opacity-0 pointer-events-none select-none'}`}>
      <div className={`relative z-10  pb-8 md:pb-16 transition-opacity duration-500 lg:scale-90 lg:origin-center xl:scale-100 2xl:scale-110 ${videoLoaded ? 'opacity-100' : 'opacity-0 pointer-events-none select-none'}`}>
        {/* Studio name */}
        <div className="text-center mb-16">
          <motion.h1
            initial={{ opacity: 0, y: 40 }}
            animate={titleControls}
            transition={{ duration: 0.6, ease:"easeInOut" }}
            className="text-[3.8em] sm:text-6xl md:text-8xl lg:text-[12rem] xl:text-[14rem] 2xl:text-[16rem] font-normal font-inter leading-none tracking-tight whitespace-nowrap mb-[-2rem] sm:mb-[-6rem] md:mb-[-8rem] lg:mb-[-10rem] xl:mb-[-12rem] 2xl:mb-[-14rem]"
            style={{
              background: 'linear-gradient(0deg, #438243 22.12%, #000000 99.99%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
              color: 'transparent',
              padding: '5px',
            }}
          >
            Fory Studio
          </motion.h1>
        </div>

        {/* Hero image with laptop */}
        <div className="px-4 animate-scale-in flex justify-center">
          <div className="relative max-w-5xl w-full -ml-[70px] lg:-ml-[180px] xl:max-w-6xl xl:-ml-[200px] 2xl:max-w-7xl 2xl:-ml-[250px]">
            <video 
              ref={videoRef}
              src="/assets/laptop.webm"
              autoPlay
              muted
              playsInline
              className="w-full rounded-lg"
              poster="/assets/Laptop.png"
              onCanPlayThrough={() => setVideoLoaded(true)}
            >
              Your browser does not support the video tag.
            </video>
          </div>
        </div>
        {/* Tagline card */}
        <div ref={taglineRef} className="flex justify-center px-4 animate-fade-in">
          <div
            id="tagline-card"
            className="tagline-full-width rounded-2xl p-6 sm:p-8 md:p-10 lg:p-12 xl:p-16 2xl:p-20 max-w-4xl xl:max-w-6xl 2xl:max-w-7xl w-full"
            style={{ backgroundColor: 'transparent' }}
          >
            <div className="text-center mb-8">
              <motion.p
                initial="hidden"
                animate={isTaglineInView ? "visible" : "hidden"}
                variants={{
                  hidden: { opacity: 0, y: 60, scale: 0.95 },
                  visible: { 
                    opacity: 1, 
                    y: 0, 
                    scale: 1,
                    transition: { 
                      duration: 1.2, 
                      ease: [0.25, 1, 0.5, 1],
                      delay: 0.3
                    } 
                  },
                }}
                className="font-semibold mb-3 pt-10 pb-5 text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl 2xl:text-7xl whitespace-nowrap"
                style={{
                  background: 'linear-gradient(180deg, #8DAE95 42.31%, #000000 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text',
                  color: 'transparent',
                  whiteSpace: 'nowrap',
                }}
              >
                You've built something great.
              </motion.p>
              <motion.p
                initial="hidden"
                animate={isTaglineInView ? "visible" : "hidden"}
                variants={{
                  hidden: { opacity: 0, y: 60, scale: 0.95 },
                  visible: { 
                    opacity: 1, 
                    y: 0, 
                    scale: 1,
                    transition: { 
                      duration: 1.2, 
                      ease: [0.25, 1, 0.5, 1],
                      delay: 0.5
                    } 
                  },
                }}
                className="font-semibold text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl 2xl:text-7xl whitespace-nowrap"
                style={{
                  background: 'linear-gradient(180deg, #8DAE95 42.31%, #000000 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text',
                  color: 'transparent',
                  whiteSpace: 'nowrap',
                }}
              >
                –We help the world see it
              </motion.p>
            </div>
            <div className="text-center">
              <motion.div
                initial="hidden"
                animate={isTaglineInView ? "visible" : "hidden"}
                variants={{
                  hidden: { opacity: 0, y: 50, scale: 1 },
                  visible: { 
                    opacity: 1, 
                    y: 0, 
                    scale: 1,
                    transition: { 
                      duration: 0.5, 
                      ease: [1, 0.5, 0.5, 1],
                      delay: 0.9
                    } 
                  },
                }}
              >
                <GradientButton className="text-base px-5 py-2 sm:text-lg sm:px-7 sm:py-2 md:text-lg md:px-8 md:py-3 xl:text-xl xl:px-10 xl:py-4 2xl:text-2xl 2xl:px-12 2xl:py-5">
                  book a call
                </GradientButton>
              </motion.div>
            </div>
          </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
