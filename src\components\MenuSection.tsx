
import React, { useRef } from 'react';
import GradientButton from './GradientButton';
import { Check, X, Sparkles } from 'lucide-react';
import { motion, useInView } from 'framer-motion';

const MenuSection: React.FC = () => {
  const services = [
    { name: 'explainer videos', available: true },
    { name: 'brand identity', available: true },
    { name: '3d mockups', available: true },
    { name: 'website design & development', available: true },
    { name: 'consulting', available: true },
    { name: 'progress check-ins', available: true },
    { name: 'professional quality', available: true },
    { name: 'motion graphics', available: true },
  ];

  const unavailable = [
    'Overwhelming, Under-Delivering',
    'Projects That Drain on for Months',
    'Cookie-Cutter Designs or Templates',
  ];

  // Framer Motion refs and inViewc
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: '-100px' });

  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0 },
    show: { opacity: 1, transition: { duration: 0.7 } },
  };
  const slideIn = {
    hidden: { opacity: 0, y: 40 },
    show: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: { duration: 0.5, delay: 0.3 + i * 0.12 },
    }),
  };

  // Calculate total items for delay
  const totalServices = services.length;
  const totalUnavailable = unavailable.length;

  return (
    <section
      id="services"
      className="py-20 pt-0 pb-16 lg:pt-0 lg:pb-32 px-4 mt-8 sm:mt-0"
      ref={ref}
    >
      <div className="max-w-4xl xl:max-w-6xl 2xl:max-w-7xl mx-auto">
        <div className="backdrop-blur-sm rounded-[40px] p-8 xl:p-12 shadow-2xl messi">
          {/* Section title */}
          <motion.div
            className="flex items-center gap-3 mb-[100px]"
            variants={fadeIn}
            initial="hidden"
            animate={isInView ? 'show' : 'hidden'}
          >
            <h2
              className="text-3xl lg:text-5xl xl:text-7xl 2xl:text-8xl"
              style={{
                background: 'linear-gradient(180deg, #000000 0%, #068657 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text',
                color: 'transparent',
              }}
            >
              The Menu
            </h2>
            <div className="flex-1 flex justify-end">
              <img src="/assets/star.png" alt="star" className="w-24 h-24 xl:w-32 xl:h-32 2xl:w-40 2xl:h-40" />
            </div>
          </motion.div>

          {/* Available services */}
          <div className="grid md:grid-cols-2 gap-8 mb-[120px]">
            {services.map((service, index) => (
              <motion.div
                key={index}
                className="flex items-center gap-3 px-10"
                custom={index}
                variants={slideIn}
                initial="hidden"
                animate={isInView ? 'show' : 'hidden'}
              >
                <img src="/assets/check.png" alt="check" className="w-5 h-5 xl:w-6 xl:h-6 2xl:w-7 2xl:h-7 flex-shrink-0" />
                <span className="text-gray-800 text-lg xl:text-2xl 2xl:text-3xl tracking-wide text-center" style={{ textShadow: '0 0 1px #000, 0 0 2px #000' }}>{service.name}</span>
              </motion.div>
            ))}
          </div>

          {/* Unavailable services */}
          <div className="mb-8">
            {unavailable.map((itemText, index) => (
              <motion.div
                key={index}
                className="flex items-center gap-3 mb-2 px-10 py-2"
                custom={totalServices + index}
                variants={slideIn}
                initial="hidden"
                animate={isInView ? 'show' : 'hidden'}
              >
                <img src="/assets/error.png" alt="error" className="w-5 h-5 xl:w-6 xl:h-6 2xl:w-7 2xl:h-7 flex-shrink-0" />
                <span className="text-red-800 text-lg xl:text-2xl 2xl:text-3xl tracking-wide text-center" style={{ textShadow: '0 0 ' }}>{itemText}</span>
              </motion.div>
            ))}
          </div>

          <motion.div
            className="flex justify-center items-center mt-20 mb-10"
            custom={totalServices + totalUnavailable}
            variants={slideIn}
            initial="hidden"
            animate={isInView ? 'show' : 'hidden'}
          >
            <GradientButton className="btn text-sm px-4 py-2 sm:text-base sm:px-6 sm:py-2 md:text-lg md:px-8 md:py-3 xl:text-xl xl:px-10 xl:py-4 2xl:text-2xl 2xl:px-12 2xl:py-5">
              <span style={{ textShadow: 'none' }}>Learn more</span>
            </GradientButton>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default MenuSection;
