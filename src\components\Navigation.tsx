import React, { useState, useEffect } from 'react';
import { motion, useAnimation, easeInOut } from 'framer-motion';

const Navigation: React.FC = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const [showNav, setShowNav] = useState(false);
  const headerControls = useAnimation();
  const linkControls = useAnimation();

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowNav(true);
      // Start header animation first
      headerControls.start('visible');
      
      // Then start link animations with delay
      setTimeout(() => {
        linkControls.start('visible');
      }, 800); // Delay after header animation
    }, 100);
    return () => clearTimeout(timer);
  }, [headerControls, linkControls]);

  useEffect(() => {
    if (menuOpen) {
      linkControls.start('visible');
    } else {
      linkControls.start('hidden');
    }
  }, [menuOpen, linkControls]);

  const handleSmoothScroll = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
    setMenuOpen(false);
  };

  // Header animation variants
  const headerVariants = {
    hidden: { 
      opacity: 0, 
      y: -20 
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: easeInOut
      }
    }
  };

  // Link animation variants
  const linkVariants = {
    hidden: { 
      opacity: 0, 
      y: -10,
      scale: 0.9
    },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.2,
        ease: easeInOut,
        delay: i * 0.15 // Stagger each link by 0.15s
      }
    })
  };

  const navLinks = [
    { id: 'home', label: 'home' },
    { id: 'gallery', label: 'gallery' },
    { id: 'services', label: 'services' },
    { id: 'reputation', label: 'reputation' }
  ];

  return (
    <motion.nav
      initial="hidden"
      className={`fixed top-0 left-0 right-0 z-50 transition-transform duration-500 ease-out
        ${showNav ? 'translate-y-0 opacity-100' : '-translate-y-16 opacity-0'}`}
      style={{ 
        willChange: 'transform, opacity',
        backgroundImage: 'url("/assets/Navbar Base.png")',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        boxShadow: 'rgba(50, 50, 93, 0.4) 0px 50px 100px -20px, rgba(0, 0, 0, 0.5) 0px 30px 60px -30px, rgba(0, 0, 0, 0.25) 0px 10px 20px, rgba(25, 21, 21, 0.1) 0px 6px 12px'
      }}
    >
      <div className="max-w-7xl mx-auto px-2 py-2 flex items-center justify-between md:justify-center">
        {/* Logo or Brand (optional) */}
        <div className="flex justify-center md:justify-start items-center">
          {/* Mobile logo */}
          <img
            src="/assets/Fory logo.png"
            alt="Fory Logo"
            className="h-8 w-auto mr-2 md:hidden"
          />
          {/* You can add a logo here if needed */}
        </div>
        {/* Hamburger menu for mobile */}
        <button
          className="md:hidden flex items-center px-2 py-1 rounded text-gray-700 hover:text-forest-green focus:outline-none"
          onClick={() => setMenuOpen(!menuOpen)}
          aria-label="Toggle navigation menu"
        >
          <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
        {/* Navigation links */}
        <div className="hidden md:flex justify-center space-x-10">
          {navLinks.map((link, index) => (
            <motion.button 
              key={link.id}
              custom={index}
              initial="hidden"
              animate={linkControls}
              variants={linkVariants}
              onClick={() => handleSmoothScroll(link.id)}
              className="text-gray-700 hover:text-forest-green transition-colors font-medium text-sm px-1 py-0.5"
            >
              {link.label}
            </motion.button>
          ))}
        </div>
        {/* Mobile menu dropdown */}
        {menuOpen && (
          <div className="absolute top-full left-0 right-0 bg-white/95 border-b border-gray-200 shadow-md flex flex-col items-center space-y-2 py-2 md:hidden">
            {navLinks.map((link, index) => (
              <motion.button 
                key={link.id}
                custom={index}
                initial="hidden"
                animate={linkControls}
                variants={linkVariants}
                onClick={() => handleSmoothScroll(link.id)}
                className="text-gray-700 hover:text-forest-green transition-colors font-medium text-sm px-1 py-0.5"
              >
                {link.label}
              </motion.button>
            ))}
          </div>
        )}
      </div>
    </motion.nav>
  );
};

export default Navigation;
