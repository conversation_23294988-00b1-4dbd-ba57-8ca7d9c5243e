
import React, { useRef, useEffect, useState } from 'react';
import GradientButton from './GradientButton';
import { motion, useInView } from 'framer-motion';

const SCROLL_DURATION = 1200; // ms
const REPEAT_SETS = 10; // Number of times to repeat the cards for seamless infinite scroll

const ReputationSection: React.FC = () => {
  // Only 3 unique images
  const baseImages = [
    '/public/assets/client left.png',
    '/public/assets/Client middle.png',
    '/public/assets/client right.png',
  ];
  // Repeat images many times for seamless infinite effect
  const testimonialImages = Array(REPEAT_SETS).fill(baseImages).flat();

  // Ref for the mobile slider
  const sliderRef = useRef<HTMLDivElement | null>(null);
  const animating = useRef(false);
  const snapTimeout = useRef<NodeJS.Timeout | null>(null);

  // Framer Motion refs and inView
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: '-100px' });

  // Simplified animation variants matching Fory Studio hero style
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.8,
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  // Update animation variants for sequential animation
  const titleVariants = {
    hidden: { 
      opacity: 0, 
      y: 30
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5, // fast
        delay: 0.1,
        ease: [0.22, 1, 0.36, 1]
      }
    }
  };

  const subtitleVariants = {
    hidden: { 
      opacity: 0, 
      y: 40
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 1.6, // slow
        delay: 0.3, // decreased delay after first line
        ease: [0.22, 1, 0.36, 1]
      }
    }
  };

  const cardVariants = {
    hidden: { 
      opacity: 0, 
      y: 60,
      scale: 0.9
    },
    visible: (i: number) => ({
      opacity: 1,
      y: -50,
      scale: 1,
      transition: {
        duration: 1.2,
        delay: 0.4 + i * 0.1
      }
    })
  };

  const buttonVariants = {
    hidden: { 
      opacity: 0, 
      y: 50,
      scale: 0.95
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.5,
        delay: 0.8
      }
    }
  };

  // Hover state for desktop testimonial cards
  const [hoveredIdx, setHoveredIdx] = useState<number | null>(null);
  // Hover state for mobile testimonial cards
  const [hoveredMobileIdx, setHoveredMobileIdx] = useState<number | null>(null);

  // Center the middle set on mount (mobile only)
  useEffect(() => {
    const slider = sliderRef.current;
    if (slider && window.innerWidth < 768) {
      setTimeout(() => {
        const cards = slider.querySelectorAll('.flex-shrink-0');
        const middleSetIdx = Math.floor(REPEAT_SETS / 2) * baseImages.length;
        const card = cards[middleSetIdx];
        if (card) {
          slider.scrollTo({
            left: (card as HTMLElement).offsetLeft - (window.innerWidth / 2) + ((card as HTMLElement).offsetWidth / 2),
            behavior: 'auto',
          });
        }
      }, 100);
    }
  }, []);

  // Animate scrollLeft to target with custom duration
  function animateScrollTo(slider: HTMLDivElement, to: number, duration: number) {
    if (animating.current) return;
    animating.current = true;
    const start = slider.scrollLeft;
    const change = to - start;
    const startTime = performance.now();
    function easeInOutQuart(x: number) {
      return x < 0.5
        ? 8 * x * x * x * x
        : 1 - Math.pow(-2 * x + 2, 4) / 2;
    }
    function animate(now: number) {
      const elapsed = now - startTime;
      const progress = Math.min(elapsed / duration, 1);
      const eased = easeInOutQuart(progress);
      slider.scrollLeft = start + change * eased;
      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        animating.current = false;
      }
    }
    requestAnimationFrame(animate);
  }

  // Infinite effect for manual scroll: seamless reset to middle set
  const handleMobileScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const slider = e.currentTarget;
    const cardWidth = slider.querySelector('.flex-shrink-0')?.clientWidth || 288;
    const totalCards = slider.querySelectorAll('.flex-shrink-0').length;
    const visibleCards = baseImages.length;
    const middleSetIdx = Math.floor(REPEAT_SETS / 2) * visibleCards;
    // If user is near the start or end, reset to the same card in the middle set (no animation)
    if (slider.scrollLeft <= cardWidth * visibleCards * 1.5) {
      // Find which card we're closest to
      const cards = slider.querySelectorAll('.flex-shrink-0');
      let minDist = Infinity;
      let snapIdx = 0;
      const sliderCenter = slider.scrollLeft + slider.offsetWidth / 2;
      cards.forEach((card, idx) => {
        const cardCenter = (card as HTMLElement).offsetLeft + (card as HTMLElement).offsetWidth / 2;
        const dist = Math.abs(cardCenter - sliderCenter);
        if (dist < minDist) {
          minDist = dist;
          snapIdx = idx;
        }
      });
      // Move to the same card in the middle set
      const targetIdx = middleSetIdx + (snapIdx % visibleCards);
      const targetCard = cards[targetIdx];
      if (targetCard) {
        slider.scrollTo({
          left: (targetCard as HTMLElement).offsetLeft - (slider.offsetWidth / 2) + ((targetCard as HTMLElement).offsetWidth / 2),
          behavior: 'auto', // instant
        });
      }
    } else if (slider.scrollLeft >= cardWidth * (totalCards - visibleCards * 2.5)) {
      // Near the end
      const cards = slider.querySelectorAll('.flex-shrink-0');
      let minDist = Infinity;
      let snapIdx = 0;
      const sliderCenter = slider.scrollLeft + slider.offsetWidth / 2;
      cards.forEach((card, idx) => {
        const cardCenter = (card as HTMLElement).offsetLeft + (card as HTMLElement).offsetWidth / 2;
        const dist = Math.abs(cardCenter - sliderCenter);
        if (dist < minDist) {
          minDist = dist;
          snapIdx = idx;
        }
      });
      // Move to the same card in the middle set
      const targetIdx = middleSetIdx + (snapIdx % visibleCards);
      const targetCard = cards[targetIdx];
      if (targetCard) {
        slider.scrollTo({
          left: (targetCard as HTMLElement).offsetLeft - (slider.offsetWidth / 2) + ((targetCard as HTMLElement).offsetWidth / 2),
          behavior: 'auto', // instant
        });
      }
    }
    // Debounced snap to nearest card after scroll ends
    if (snapTimeout.current) clearTimeout(snapTimeout.current);
    if (animating.current) return;
    snapTimeout.current = setTimeout(() => {
      // Snap to nearest card
      const cards = slider.querySelectorAll('.flex-shrink-0');
      let minDist = Infinity;
      let snapIdx = 0;
      const sliderCenter = slider.scrollLeft + slider.offsetWidth / 2;
      cards.forEach((card, idx) => {
        const cardCenter = (card as HTMLElement).offsetLeft + (card as HTMLElement).offsetWidth / 2;
        const dist = Math.abs(cardCenter - sliderCenter);
        if (dist < minDist) {
          minDist = dist;
          snapIdx = idx;
        }
      });
      const snapCard = cards[snapIdx];
      if (snapCard) {
        const target = (snapCard as HTMLElement).offsetLeft - (slider.offsetWidth / 2) + ((snapCard as HTMLElement).offsetWidth / 2);
        animateScrollTo(slider, target, SCROLL_DURATION);
      }
    }, 120);
  };

  return (
    <motion.section 
      id="reputation" 
      className="py-20 pt-0 lg:pt-32 lg:pb-32 px-4 mb-16" 
      ref={ref}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.2, margin: "-100px" }}
      variants={containerVariants}
    >
      <div className="max-w-7xl xl:max-w-8xl 2xl:max-w-[110rem] mx-auto lg:scale-90 lg:origin-center">
        <motion.div
          className="flex flex-col items-start justify-center text-left mb-12"
          variants={containerVariants}
        >
          {/* First line: revert to solid black text */}
          <motion.h2
            className="text-3xl sm:text-4xl md:text-5xl lg:text-7xl xl:text-8xl 2xl:text-9xl text-black leading-tight mb-2"
            variants={titleVariants}
            initial="hidden"
            animate={isInView ? "visible" : "hidden"}
          >
            Reputation is everything
          </motion.h2>
          {/* Second line: revert to original black-to-green gradient */}
          <motion.h3
            className="text-3xl sm:text-4xl md:text-5xl lg:text-7xl xl:text-8xl 2xl:text-9xl leading-tight bg-gradient-to-b from-black to-forest-green bg-clip-text text-transparent"
            style={{
              background: 'linear-gradient(180deg, #000000 0%, #068657 84.13%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
              color: 'transparent',
            }}
            variants={subtitleVariants}
            initial="hidden"
            animate={isInView ? "visible" : "hidden"}
          >
            Mine speaks for itself
          </motion.h3>
        </motion.div>

        {/* Testimonial images as slider on mobile, grid on md+ */}
        <motion.div
          className="mb-[150px] lg:-mt-[60px]"
          variants={containerVariants}
        >
          {/* Desktop: Only show 3 unique images */}
          <div className="hidden md:grid md:grid-cols-3 gap-7">
            {isInView && baseImages.map((img, idx) => (
              <motion.div
                key={idx}
                className="flex items-stretch justify-center rounded-3xl overflow-hidden shadow-md bg-white/10 border border-white/20 backdrop-blur-2xl cursor-pointer"
                style={{ minHeight: '320px', backdropFilter: 'blur(32px)' }}
                initial={{ y: 300, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                animate={{
                  scale: hoveredIdx === idx && window.innerWidth >= 768 ? 1.03 : 1
                }}
                transition={{
                  y: { duration: 2.3, ease: [0.22, 1, 0.36, 1], delay: 0.12 * idx },
                  opacity: { duration: 2.3, ease: [0.22, 1, 0.36, 1], delay: 0.12 * idx },
                  scale: { duration: 0.25, ease: 'easeOut' }
                }}
                viewport={{ once: true, amount: 0.3 }}
                onMouseEnter={() => { if (window.innerWidth >= 768) setHoveredIdx(idx); }}
                onMouseLeave={() => { if (window.innerWidth >= 768) setHoveredIdx(null); }}
                whileTap={{ 
                  scale: window.innerWidth >= 768 ? 0.98 : 1,
                  transition: { duration: 0.1, ease: "easeOut" }
                }}
              >
                <img
                  src={img.replace('/public', '')}
                  alt={`Client testimonial ${idx + 1}`}
                  className="w-full h-full object-cover rounded-3xl transition-all duration-300"
                  style={{ aspectRatio: '1/1', background: '#fff' }}
                  draggable="false"
                  onError={(e) => { e.currentTarget.style.background = '#fff'; }}
                />
              </motion.div>
            ))}
          </div>
          {/* Mobile slider - infinite effect, center middle card, hide scrollbar, slow smooth scroll by hand */}
          <div
            className="md:hidden flex gap-4 overflow-x-auto snap-x snap-mandatory hide-scrollbar"
            style={{
              WebkitOverflowScrolling: 'touch',
              scrollbarWidth: 'none',
              msOverflowStyle: 'none',
              scrollBehavior: 'smooth',
              touchAction: 'pan-x',
              scrollSnapType: 'x mandatory',
              scrollPaddingLeft: 'calc(50vw - 9rem)', // 9rem = w-72/2
              transition: 'scroll-left 2.5s cubic-bezier(0.05, 0.6, 0.1, 1)',
            }}
            ref={sliderRef}
            onScroll={handleMobileScroll}
          >
            {/* Hide scrollbar for all browsers */}
            <style>{`
              .hide-scrollbar::-webkit-scrollbar { display: none; }
              .hide-scrollbar { -ms-overflow-style: none; scrollbar-width: none; }
            `}</style>
            {testimonialImages.map((img, idx) => (
              <motion.div
                key={idx}
                className="flex-shrink-0 w-72 flex items-stretch justify-center rounded-3xl overflow-hidden shadow-md bg-white/10 border border-white/20 backdrop-blur-2xl snap-center cursor-pointer"
                style={{ minHeight: '320px', backdropFilter: 'blur(32px)' }}
                initial={{ y: 100, opacity: 0 }}
                whileInView={{ y: -40, opacity: 1 }}
                animate={{
                  scale: hoveredMobileIdx === idx && window.innerWidth < 768 ? 1.03 : 1,
                  y: 0
                }}
                transition={{
                  y: { type: "spring", stiffness: 60, damping: 12 },
                  opacity: { duration: 0.8 },
                  scale: { duration: 0.25, ease: 'easeOut' }
                }}
                viewport={{ once: true, amount: 0.3 }}
                onMouseEnter={() => { if (window.innerWidth < 768) setHoveredMobileIdx(idx); }}
                onMouseLeave={() => { if (window.innerWidth < 768) setHoveredMobileIdx(null); }}
                whileTap={{ 
                  scale: window.innerWidth < 768 ? 0.98 : 1,
                  transition: { duration: 0.1, ease: "easeOut" }
                }}
              >
                <img
                  src={img.replace('/public', '')}
                  alt={`Client testimonial ${(idx % 3) + 1}`}
                  className="w-full h-full object-cover rounded-3xl transition-all duration-300"
                  style={{ aspectRatio: '1/1', background: '#fff' }}
                  draggable="false"
                  onError={(e) => { e.currentTarget.style.background = '#fff'; }}
                />
              </motion.div>
            ))}
          </div>
        </motion.div>

        <motion.div
          className="text-center mb-15 mt-[250px]" // Increased margin below the button
          variants={buttonVariants}
        >
          <GradientButton className="text-base px-6 py-3 sm:text-lg sm:px-8 sm:py-3 md:text-xl md:px-10 md:py-4 xl:text-2xl xl:px-12 xl:py-5 2xl:text-3xl 2xl:px-16 2xl:py-6">
            book a call
          </GradientButton>
        </motion.div>
      </div>
    </motion.section>
  );
};

export default ReputationSection;
