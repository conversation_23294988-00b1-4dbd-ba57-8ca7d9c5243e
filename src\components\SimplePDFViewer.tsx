import React, { useState, useEffect } from 'react';

interface SimplePDFViewerProps {
  pdfUrl: string;
}

const SimplePDFViewer: React.FC<SimplePDFViewerProps> = ({ pdfUrl }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Test if PDF is accessible
    const testPDF = async () => {
      try {
        const response = await fetch(pdfUrl);
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        setLoading(false);
      } catch (err) {
        console.error('PDF access error:', err);
        setError(err instanceof Error ? err.message : 'Failed to load PDF');
        setLoading(false);
      }
    };

    testPDF();
  }, [pdfUrl]);

  if (loading) {
    return (
      <div className="pdf-loading">
        <div className="loading-spinner"></div>
        <h3>Loading PDF...</h3>
        <p>Please wait while we prepare the document</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="pdf-error">
        <h3>Error Loading PDF</h3>
        <p>{error}</p>
        <button 
          onClick={() => window.location.reload()} 
          style={{
            marginTop: '16px',
            padding: '8px 16px',
            background: 'rgba(255,255,255,0.1)',
            border: '1px solid rgba(255,255,255,0.2)',
            borderRadius: '4px',
            color: 'white',
            cursor: 'pointer'
          }}
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="simple-pdf-viewer">
      <iframe
        src={`${pdfUrl}#toolbar=0&navpanes=0&scrollbar=1&view=FitH`}
        width="100%"
        height="100%"
        style={{
          border: 'none',
          borderRadius: '8px',
          background: '#fff'
        }}
        title="PDF Document"
        onLoad={() => setLoading(false)}
        onError={() => setError('Failed to load PDF in iframe')}
      />
    </div>
  );
};

export default SimplePDFViewer;
