@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
    box-sizing: border-box;
  }

  /* Prevent horizontal scroll globally */
  html, body {
    overflow-x: hidden !important;
    max-width: 100vw !important;
  }

  body {
    @apply bg-background text-foreground;
    background-image: url('/assets/curvetry.png');
    background-size: 100% 100%;      /* Stretch to fill both width and height */
    background-position: center center;
    background-repeat: no-repeat;
    min-height: 100vh;
    width: 100vw;
    overflow-x: hidden !important;
    position: relative;
    zoom:100%;
    scale: 1;
  }

  html {
    overflow-x: hidden !important;
    max-width: 100vw;
  }

  * {
    max-width: 100vw;
    box-sizing: border-box;
  }
  
  body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    pointer-events: none;
    z-index: 0;
    /* Optional: adjust overlay color and opacity as needed */
    /* background: rgba(0,0,0,0.2); */
  }
}
.board{
background-image: url('/assets/The-menu-board.png');
background-position: center;
background-repeat: no-repeat;
height: 100%;
width: 100%;
}
.board-built{
background-image: url('/assets/intenet-browser.png');
background-position: bottom;
background-repeat: no-repeat;
height: 100%;
width: 100%;
}

.tagline-full-width{
background-image: url('/assets/intenet-browser.png');
background-position: top;
background-repeat: no-repeat;
background-size: 100% auto;
background-color: white !important;
border-radius: 40px;
height: 100%;
width: 100%;
min-height: 200px;
}
.messi{
background-image: url('/assets/intenet-browser.png');
background-position: top;
background-repeat: no-repeat;
background-size: 100% auto;
background-color: white !important;
border-radius: 40px;
height: 100%;
width: 100%;
min-height: 200px;
}

@media (min-width: 1280px) {
  .board-built {
    background-size: 200% auto;
  }
}
@media (min-width: 1536px) {
  .board-built {
    background-size: 250% auto;
  }
}

@media (max-width: 600px) {
  body {
    background-size: contain;      /* Show full image on mobile */
    background-position: top center;
    background-repeat: repeat-y; /* Repeat vertically to fill mobile screen */
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }
  body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    pointer-events: none;
    z-index: 0;
    background: rgba(34, 64, 34, 0.18); /* Lighter green overlay for more visible background */
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
  }
  
  .tagline-full-width {
    background-image: none !important;
  }
}

.main-container {
  margin-left: auto;
  margin-right: auto;
  max-width: 1200px; /* or whatever width you want */
}

.nav-bottom-gradient {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 10px; /* Adjust height as needed */
  pointer-events: none;
  z-index: 10;
  background: linear-gradient(180deg, rgba(219,231,226,0) 0%, #dbe7e2 100%);
}

.messi{
background-image: url('/assets/The-menu-board.png');
background-size: 200% 200%;
background-position: center;
border-radius: 20px;
background-repeat: no-repeat;
min-height: 400px;
z-index: 9999;
}

/* Centered Modal Styles - Centers based on user's current scroll position */
.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  min-height: 100vh;
  width: 100vw;
  max-width: 100vw;
  z-index: 999999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
  overflow-x: hidden !important;
  box-sizing: border-box;
}

.modal-overlay-open {
  opacity: 1;
  visibility: visible;
}

.modal-overlay-close {
  opacity: 0;
  visibility: hidden;
}

.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0);
  cursor: pointer;
  z-index: 999998;
  transition: background-color 0.3s ease;
  overflow: hidden !important;
  max-width: 100vw;
  box-sizing: border-box;
}

.modal-backdrop-open {
  background-color: rgba(0, 0, 0, 0.95);
}

.modal-backdrop-close {
  background-color: rgba(0, 0, 0, 0);
}

.modal-container {
  position: relative;
  z-index: 1000000;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100vh;
  min-height: 100vh;
  max-width: 100vw;
  overflow-x: hidden !important;
  overflow-y: hidden;
  box-sizing: border-box;
}

.modal-content {
  background-color: #000;
  border-radius: 16px;
  padding: 20px;
  position: relative;
  max-width: 1200px;
  width: 95%;
  min-height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.15);
  transform: scale(0.7);
  opacity: 0;
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), opacity 0.3s ease;
  max-width: calc(100vw - 30px);
  max-height: calc(100vh - 30px);
  overflow: hidden;
}

/* PDF Modal specific styles */
.modal-content:has(.modal-pdf-viewer) {
  max-width: 95vw;
  max-height: 95vh;
  height: 95vh;
  padding: 0;
  background-color: #1a1a1a;
  display: flex;
  flex-direction: column;
}

.modal-content-open {
  transform: scale(1);
  opacity: 1;
}

.modal-content-close {
  transform: scale(0.7);
  opacity: 0;
}

.modal-close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  background-color: rgba(0, 0, 0, 0.8);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  z-index: 1000001;
}

/* Close button for PDF viewer */
.modal-content:has(.modal-pdf-viewer) .modal-close-btn {
  background-color: rgba(0, 0, 0, 0.9);
  border: 2px solid rgba(255, 255, 255, 0.3);
  top: 15px;
  right: 15px;
}

.modal-close-btn:hover {
  background-color: rgba(255, 255, 255, 0.25);
  transform: scale(1.1);
}

.modal-body {
  text-align: center;
  color: white;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

/* Modal body for PDF viewer - full height */
.modal-content:has(.modal-pdf-viewer) .modal-body {
  height: 100%;
  flex: 1;
  display: flex;
  align-items: stretch;
  justify-content: stretch;
}

.modal-video-container {
  width: 100%;
  height: 500px;
  border-radius: 12px;
  overflow: hidden;
  background: #000;
}

.modal-iframe {
  border-radius: 12px;
  background: #000;
}

/* Custom PDF Viewer Styles */
.modal-pdf-viewer {
  width: 100%;
  height: 100%;
  min-height: 100%;
  display: flex;
  flex-direction: column;
  background: #1a1a1a;
  border-radius: 16px;
  overflow: hidden;
  position: relative;
  flex: 1;
}

.simple-pdf-viewer {
  width: 100%;
  height: 100%;
  min-height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  flex: 1;
}

.simple-pdf-viewer iframe {
  width: 100%;
  height: 100%;
  min-height: 70vh;
  flex: 1;
  border: none;
  border-radius: 8px;
  background: #fff;
}

.custom-pdf-viewer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.pdf-pages-scroll {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  background: #1a1a1a;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.pdf-pages-scroll::-webkit-scrollbar {
  width: 8px;
}

.pdf-pages-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.pdf-pages-scroll::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

.pdf-pages-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

.pdf-page-container {
  width: 100%;
  max-width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 10px;
  padding: 0 20px;
  box-sizing: border-box;
}

.pdf-page {
  width: 100% !important;
  height: auto !important;
  max-width: 100%;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  overflow: hidden;
}

.pdf-page canvas {
  width: 100% !important;
  height: auto !important;
  display: block;
  border-radius: 8px;
}

.pdf-loading,
.pdf-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: white;
  text-align: center;
}

.pdf-loading h3,
.pdf-error h3 {
  font-size: 24px;
  margin-bottom: 12px;
  font-weight: 600;
}

.pdf-loading p,
.pdf-error p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 16px;
}

.page-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: rgba(255, 255, 255, 0.7);
}

.page-loading p {
  margin-top: 10px;
  font-size: 14px;
}

.loading-spinner,
.loading-spinner-small {
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
}

.loading-spinner-small {
  width: 24px;
  height: 24px;
  margin-bottom: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.modal-fallback {
  text-align: center;
}

.modal-title {
  font-size: 42px;
  margin-bottom: 20px;
  font-weight: 600;
  letter-spacing: -0.025em;
}

.modal-subtitle {
  color: rgba(255, 255, 255, 0.7);
  font-size: 20px;
  font-weight: 400;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 5px;
  }

  .modal-content {
    padding: 15px;
    margin: 5px;
    min-height: 400px;
    max-width: calc(100vw - 10px);
    max-height: calc(100vh - 10px);
  }

  .modal-content:has(.modal-pdf-viewer) {
    max-width: 98vw;
    max-height: 98vh;
    height: 98vh;
    padding: 0;
  }

  .modal-video-container {
    height: 350px;
  }

  .pdf-pages-scroll {
    padding: 10px 0;
    gap: 15px;
  }

  .modal-close-btn {
    top: 10px;
    right: 10px;
    width: 40px;
    height: 40px;
  }

  .modal-content:has(.modal-pdf-viewer) .modal-close-btn {
    top: 10px;
    right: 10px;
    width: 45px;
    height: 45px;
  }

  .modal-title {
    font-size: 32px;
    margin-bottom: 16px;
  }

  .modal-subtitle {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .modal-overlay {
    padding: 2px;
  }

  .modal-content {
    padding: 10px;
    min-height: 300px;
    max-width: calc(100vw - 4px);
    max-height: calc(100vh - 4px);
  }

  .modal-content:has(.modal-pdf-viewer) {
    max-width: 99vw;
    max-height: 99vh;
    height: 99vh;
    padding: 0;
  }

  .modal-video-container {
    height: 280px;
  }

  .pdf-pages-scroll {
    padding: 5px 0;
    gap: 10px;
  }

  .modal-close-btn {
    top: 8px;
    right: 8px;
    width: 36px;
    height: 36px;
  }

  .modal-content:has(.modal-pdf-viewer) .modal-close-btn {
    top: 8px;
    right: 8px;
    width: 40px;
    height: 40px;
  }

  .modal-title {
    font-size: 28px;
  }

  .modal-subtitle {
    font-size: 16px;
  }
}